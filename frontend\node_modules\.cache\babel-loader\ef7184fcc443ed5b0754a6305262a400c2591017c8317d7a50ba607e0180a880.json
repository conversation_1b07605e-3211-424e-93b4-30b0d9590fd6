{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LLM\\\\frontend\\\\src\\\\components\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = ({\n  onLogin\n}) => {\n  _s();\n  const [username, setUsername] = useState('');\n  const [email, setEmail] = useState('');\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (username.trim() && email.trim()) {\n      onLogin(username.trim(), email.trim());\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: /*#__PURE__*/_jsxDEV(\"form\", {\n      className: \"login-form\",\n      onSubmit: handleSubmit,\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Welcome to \"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"username\",\n          children: \"Username:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"text\",\n          id: \"username\",\n          value: username,\n          onChange: e => setUsername(e.target.value),\n          placeholder: \"Enter your username\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"form-group\",\n        children: [/*#__PURE__*/_jsxDEV(\"label\", {\n          htmlFor: \"email\",\n          children: \"Email:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 30,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          id: \"email\",\n          value: email,\n          onChange: e => setEmail(e.target.value),\n          placeholder: \"Enter your email\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        type: \"submit\",\n        className: \"login-button\",\n        children: \"Start Chatting\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"Jb05XbwG3/iYmYIqt0Sy0Xau1BY=\");\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "onLogin", "_s", "username", "setUsername", "email", "setEmail", "handleSubmit", "e", "preventDefault", "trim", "className", "children", "onSubmit", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "htmlFor", "type", "id", "value", "onChange", "target", "placeholder", "required", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LLM/frontend/src/components/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\r\n\r\nconst Login = ({ onLogin }) => {\r\n  const [username, setUsername] = useState('');\r\n  const [email, setEmail] = useState('');\r\n\r\n  const handleSubmit = (e) => {\r\n    e.preventDefault();\r\n    if (username.trim() && email.trim()) {\r\n      onLogin(username.trim(), email.trim());\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"login-container\">\r\n      <form className=\"login-form\" onSubmit={handleSubmit}>\r\n        <h2>Welcome to </h2>\r\n        <div className=\"form-group\">\r\n          <label htmlFor=\"username\">Username:</label>\r\n          <input\r\n            type=\"text\"\r\n            id=\"username\"\r\n            value={username}\r\n            onChange={(e) => setUsername(e.target.value)}\r\n            placeholder=\"Enter your username\"\r\n            required\r\n          />\r\n        </div>\r\n        <div className=\"form-group\">\r\n          <label htmlFor=\"email\">Email:</label>\r\n          <input\r\n            type=\"email\"\r\n            id=\"email\"\r\n            value={email}\r\n            onChange={(e) => setEmail(e.target.value)}\r\n            placeholder=\"Enter your email\"\r\n            required\r\n          />\r\n        </div>\r\n        <button type=\"submit\" className=\"login-button\">\r\n          Start Chatting\r\n        </button>\r\n      </form>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Login;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,KAAK,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EAAAC,EAAA;EAC7B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGP,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACQ,KAAK,EAAEC,QAAQ,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMU,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAIN,QAAQ,CAACO,IAAI,CAAC,CAAC,IAAIL,KAAK,CAACK,IAAI,CAAC,CAAC,EAAE;MACnCT,OAAO,CAACE,QAAQ,CAACO,IAAI,CAAC,CAAC,EAAEL,KAAK,CAACK,IAAI,CAAC,CAAC,CAAC;IACxC;EACF,CAAC;EAED,oBACEX,OAAA;IAAKY,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9Bb,OAAA;MAAMY,SAAS,EAAC,YAAY;MAACE,QAAQ,EAAEN,YAAa;MAAAK,QAAA,gBAClDb,OAAA;QAAAa,QAAA,EAAI;MAAW;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACpBlB,OAAA;QAAKY,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBb,OAAA;UAAOmB,OAAO,EAAC,UAAU;UAAAN,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC3ClB,OAAA;UACEoB,IAAI,EAAC,MAAM;UACXC,EAAE,EAAC,UAAU;UACbC,KAAK,EAAElB,QAAS;UAChBmB,QAAQ,EAAGd,CAAC,IAAKJ,WAAW,CAACI,CAAC,CAACe,MAAM,CAACF,KAAK,CAAE;UAC7CG,WAAW,EAAC,qBAAqB;UACjCC,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNlB,OAAA;QAAKY,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBb,OAAA;UAAOmB,OAAO,EAAC,OAAO;UAAAN,QAAA,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACrClB,OAAA;UACEoB,IAAI,EAAC,OAAO;UACZC,EAAE,EAAC,OAAO;UACVC,KAAK,EAAEhB,KAAM;UACbiB,QAAQ,EAAGd,CAAC,IAAKF,QAAQ,CAACE,CAAC,CAACe,MAAM,CAACF,KAAK,CAAE;UAC1CG,WAAW,EAAC,kBAAkB;UAC9BC,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNlB,OAAA;QAAQoB,IAAI,EAAC,QAAQ;QAACR,SAAS,EAAC,cAAc;QAAAC,QAAA,EAAC;MAE/C;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACf,EAAA,CA3CIF,KAAK;AAAA0B,EAAA,GAAL1B,KAAK;AA6CX,eAAeA,KAAK;AAAC,IAAA0B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}