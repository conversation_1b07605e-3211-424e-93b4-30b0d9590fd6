{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = 'http://localhost:5000/api';\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\nexport const chatAPI = {\n  sendMessage: data => api.post('/chat', data),\n  streamMessage: data => api.post('/chat/stream', data),\n  sendAgentMessage: data => api.post('/chat/agent', data),\n  getConversations: userId => api.get(`/conversations?user_id=${userId}`),\n  getConversation: conversationId => api.get(`/conversation/${conversationId}`)\n};\nexport const agentAPI = {\n  getTools: () => api.get('/agent/tools')\n};\nexport const langchainAPI = {\n  getMemory: () => api.get('/langchain/memory')\n};\nexport const ragAPI = {\n  addDocuments: data => api.post('/rag/documents', data),\n  searchDocuments: data => api.post('/rag/search', data),\n  advancedRAG: data => api.post('/rag/advanced', data)\n};\nexport const authAPI = {\n  login: data => api.post('/auth/login', data)\n};\nexport default api;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "api", "create", "baseURL", "headers", "chatAPI", "sendMessage", "data", "post", "streamMessage", "sendAgentMessage", "getConversations", "userId", "get", "getConversation", "conversationId", "agentAPI", "getTools", "langchainAPI", "get<PERSON><PERSON>ory", "ragAPI", "addDocuments", "searchDocuments", "advancedRAG", "authAPI", "login"], "sources": ["C:/Users/<USER>/Deep Learning/LLM/frontend/src/services/api.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\nconst API_BASE_URL = 'http://localhost:5000/api';\r\n\r\nconst api = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\nexport const chatAPI = {\r\n  sendMessage: (data) => api.post('/chat', data),\r\n  streamMessage: (data) => api.post('/chat/stream', data),\r\n  sendAgentMessage: (data) => api.post('/chat/agent', data),\r\n  getConversations: (userId) => api.get(`/conversations?user_id=${userId}`),\r\n  getConversation: (conversationId) => api.get(`/conversation/${conversationId}`),\r\n};\r\n\r\nexport const agentAPI = {\r\n  getTools: () => api.get('/agent/tools'),\r\n};\r\n\r\nexport const langchainAPI = {\r\n  getMemory: () => api.get('/langchain/memory'),\r\n};\r\n\r\nexport const ragAPI = {\r\n  addDocuments: (data) => api.post('/rag/documents', data),\r\n  searchDocuments: (data) => api.post('/rag/search', data),\r\n  advancedRAG: (data) => api.post('/rag/advanced', data),\r\n};\r\n\r\nexport const authAPI = {\r\n  login: (data) => api.post('/auth/login', data),\r\n};\r\n\r\nexport default api;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAG,2BAA2B;AAEhD,MAAMC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC;EACvBC,OAAO,EAAEH,YAAY;EACrBI,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AAEF,OAAO,MAAMC,OAAO,GAAG;EACrBC,WAAW,EAAGC,IAAI,IAAKN,GAAG,CAACO,IAAI,CAAC,OAAO,EAAED,IAAI,CAAC;EAC9CE,aAAa,EAAGF,IAAI,IAAKN,GAAG,CAACO,IAAI,CAAC,cAAc,EAAED,IAAI,CAAC;EACvDG,gBAAgB,EAAGH,IAAI,IAAKN,GAAG,CAACO,IAAI,CAAC,aAAa,EAAED,IAAI,CAAC;EACzDI,gBAAgB,EAAGC,MAAM,IAAKX,GAAG,CAACY,GAAG,CAAC,0BAA0BD,MAAM,EAAE,CAAC;EACzEE,eAAe,EAAGC,cAAc,IAAKd,GAAG,CAACY,GAAG,CAAC,iBAAiBE,cAAc,EAAE;AAChF,CAAC;AAED,OAAO,MAAMC,QAAQ,GAAG;EACtBC,QAAQ,EAAEA,CAAA,KAAMhB,GAAG,CAACY,GAAG,CAAC,cAAc;AACxC,CAAC;AAED,OAAO,MAAMK,YAAY,GAAG;EAC1BC,SAAS,EAAEA,CAAA,KAAMlB,GAAG,CAACY,GAAG,CAAC,mBAAmB;AAC9C,CAAC;AAED,OAAO,MAAMO,MAAM,GAAG;EACpBC,YAAY,EAAGd,IAAI,IAAKN,GAAG,CAACO,IAAI,CAAC,gBAAgB,EAAED,IAAI,CAAC;EACxDe,eAAe,EAAGf,IAAI,IAAKN,GAAG,CAACO,IAAI,CAAC,aAAa,EAAED,IAAI,CAAC;EACxDgB,WAAW,EAAGhB,IAAI,IAAKN,GAAG,CAACO,IAAI,CAAC,eAAe,EAAED,IAAI;AACvD,CAAC;AAED,OAAO,MAAMiB,OAAO,GAAG;EACrBC,KAAK,EAAGlB,IAAI,IAAKN,GAAG,CAACO,IAAI,CAAC,aAAa,EAAED,IAAI;AAC/C,CAAC;AAED,eAAeN,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}