import React, { useState, useRef, useEffect } from 'react';
import Message from './Message';
import ToolsPanel from './ToolsPanel';
import { chatAPI } from '../services/api';
import './ChatInterface.css';

const ChatInterface = ({ 
  conversationId, 
  messages, 
  onNewMessage, 
  onConversationCreate,
  user 
}) => {
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [useStreaming, setUseStreaming] = useState(false);
  const [enabledTools, setEnabledTools] = useState(['rag']);
  const [systemPrompt, setSystemPrompt] = useState('');
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const messageToSend = inputMessage.trim();
    setInputMessage('');
    setIsLoading(true);

    // Add user message immediately
    const userMessage = {
      role: 'user',
      content: messageToSend,
      timestamp: new Date().toISOString()
    };
    onNewMessage(userMessage);

    try {
      const useAgent = enabledTools.includes('agent');
      const useRAG = enabledTools.includes('rag');

      if (useStreaming) {
        await handleStreamingResponse(messageToSend, useRAG);
      } else if (useAgent) {
        await handleAgentResponse(messageToSend);
      } else {
        await handleRegularResponse(messageToSend, useRAG);
      }
    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage = {
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date().toISOString()
      };
      onNewMessage(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRegularResponse = async (message, useRAG) => {
    const response = await chatAPI.sendMessage({
      user_id: user.id,
      conversation_id: conversationId,
      message: message,
      use_rag: useRAG,
      use_agent: enabledTools.includes('agent'),
      system_prompt: systemPrompt || undefined
    });

    const assistantMessage = {
      role: 'assistant',
      content: response.data.response,
      timestamp: new Date().toISOString(),
      metadata: {
        used_agent: response.data.used_agent,
        used_rag: response.data.used_rag
      }
    };
    onNewMessage(assistantMessage);

    if (!conversationId && response.data.conversation_id) {
      onConversationCreate(response.data.conversation_id);
    }
  };

  const handleAgentResponse = async (message) => {
    const response = await chatAPI.sendAgentMessage({
      user_id: user.id,
      conversation_id: conversationId,
      message: message,
      tools: enabledTools
    });

    const assistantMessage = {
      role: 'assistant',
      content: response.data.response,
      timestamp: new Date().toISOString(),
      metadata: {
        used_agent: true,
        tools_used: response.data.tools_used
      }
    };
    onNewMessage(assistantMessage);

    if (!conversationId && response.data.conversation_id) {
      onConversationCreate(response.data.conversation_id);
    }
  };

  const handleStreamingResponse = async (message, useRAG) => {
    let fullResponse = '';
    const assistantMessage = {
      role: 'assistant',
      content: '',
      timestamp: new Date().toISOString(),
      metadata: {
        used_rag: useRAG,
        streaming: true
      }
    };
    
    onNewMessage(assistantMessage);
    const messageIndex = messages.length;

    try {
      const response = await fetch('http://localhost:5000/api/chat/stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          user_id: user.id,
          conversation_id: conversationId,
          message: message,
          use_rag: useRAG,
        }),
      });

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = JSON.parse(line.slice(6));
            
            if (data.chunk) {
              fullResponse += data.chunk;
              onNewMessage({
                ...assistantMessage,
                content: fullResponse
              }, messageIndex);
            }
            
            if (data.complete) {
              if (!conversationId) {
                // Handle new conversation creation if needed
              }
              return;
            }
          }
        }
      }
    } catch (error) {
      console.error('Streaming error:', error);
      onNewMessage({
        ...assistantMessage,
        content: 'Error: Failed to get streaming response.'
      }, messageIndex);
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleToolsChange = (tools) => {
    setEnabledTools(tools);
  };

  return (
    <div className="chat-interface">
      <div className="chat-header">
        <div className="header-title">
          <span className="header-icon">🧠</span>
          <h3>Sozhaa Tech AI</h3>
          <span className="header-subtitle">Powered by Advanced AI</span>
        </div>
        <div className="chat-controls">
          <label>
            <input
              type="checkbox"
              checked={useStreaming}
              onChange={(e) => setUseStreaming(e.target.checked)}
            />
            Use Streaming
          </label>
        </div>
      </div>

      <ToolsPanel 
        onToolsChange={handleToolsChange}
        enabledTools={enabledTools}
      />

      <div className="system-prompt-input">
        <input
          type="text"
          value={systemPrompt}
          onChange={(e) => setSystemPrompt(e.target.value)}
          placeholder="System prompt (optional)..."
          className="system-prompt-field"
        />
      </div>

      <div className="messages-container">
        {messages.length === 0 ? (
          <div className="empty-state">
            <div className="welcome-icon">🚀</div>
            <h2>Welcome to Sozhaa Tech AI</h2>
            <p>Your intelligent conversation partner powered by advanced AI technology.</p>
            <div className="features-list">
              <div className="feature-item">
                <strong>🤖 LangChain Powered</strong>
                <span>Advanced AI with memory and tools</span>
              </div>
              <div className="feature-item">
                <strong>🔍 RAG Enabled</strong>
                <span>Search your knowledge base</span>
              </div>
              <div className="feature-item">
                <strong>🛠️ Agent Tools</strong>
                <span>Web search, calculations, and more</span>
              </div>
            </div>
          </div>
        ) : (
          messages.map((message, index) => (
            <Message
              key={index}
              message={message}
              isUser={message.role === 'user'}
            />
          ))
        )}
        <div ref={messagesEndRef} />
        {isLoading && (
          <div className="loading-indicator">
            <div className="typing-dots">
              <span></span>
              <span></span>
              <span></span>
            </div>
            {enabledTools.includes('agent') && (
              <div className="agent-thinking">Agent is thinking and using tools...</div>
            )}
          </div>
        )}
      </div>

      <div className="input-area">
        <div className="input-container">
          <textarea
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Type your message here..."
            rows="1"
            disabled={isLoading}
          />
          <button
            onClick={handleSendMessage}
            disabled={isLoading || !inputMessage.trim()}
          >
            Send
          </button>
        </div>
        <div className="active-tools">
          {enabledTools.includes('rag') && <span className="tool-tag">Knowledge Base</span>}
          {enabledTools.includes('agent') && <span className="tool-tag">AI Agent</span>}
          {useStreaming && <span className="tool-tag">Streaming</span>}
        </div>
      </div>
    </div>
  );
};

export default ChatInterface;