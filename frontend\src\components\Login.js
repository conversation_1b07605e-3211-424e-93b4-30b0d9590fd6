import React, { useState } from 'react';

const Login = ({ onLogin }) => {
  const [username, setUsername] = useState('deepak');
  const [email, setEmail] = useState('<EMAIL>');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (username.trim() && email.trim()) {
      setIsLoading(true);
      try {
        await onLogin(username.trim(), email.trim());
      } catch (error) {
        console.error('Login error:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleDemoLogin = () => {
    setUsername('deepak');
    setEmail('<EMAIL>');
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="floating-shapes">
          <div className="shape shape-1"></div>
          <div className="shape shape-2"></div>
          <div className="shape shape-3"></div>
          <div className="shape shape-4"></div>
        </div>
      </div>

      <div className="login-content">
        <div className="login-form-container">
          <div className="login-header">
            <div className="logo">
              <div className="logo-icon">🤖</div>
              <h1>Sozhaa AI</h1>
            </div>
            <p className="subtitle">Your Intelligent Conversation Partner</p>
          </div>

          <form className="login-form" onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="username">
                <span className="label-icon">👤</span>
                Username
              </label>
              <input
                type="text"
                id="username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter your username"
                required
                className="form-input"
              />
            </div>

            <div className="form-group">
              <label htmlFor="email">
                <span className="label-icon">📧</span>
                Email Address
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
                required
                className="form-input"
              />
            </div>

            <button
              type="submit"
              className={`login-button ${isLoading ? 'loading' : ''}`}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <span className="spinner"></span>
                  Connecting...
                </>
              ) : (
                <>
                  <span className="button-icon">🚀</span>
                  Start Chatting
                </>
              )}
            </button>

            <div className="demo-section">
              <p className="demo-text">Try with demo credentials:</p>
              <button
                type="button"
                className="demo-button"
                onClick={handleDemoLogin}
              >
                Use Demo Account
              </button>
            </div>
          </form>

          <div className="features">
            <div className="feature">
              <span className="feature-icon">💬</span>
              <span>Smart Conversations</span>
            </div>
            <div className="feature">
              <span className="feature-icon">🧠</span>
              <span>AI-Powered Responses</span>
            </div>
            <div className="feature">
              <span className="feature-icon">📚</span>
              <span>Knowledge Base Integration</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;