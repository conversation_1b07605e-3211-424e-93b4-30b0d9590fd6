import os
import google.generativeai as genai
from dotenv import load_dotenv
import json

load_dotenv()

class LangChainService:
    def __init__(self):
        self.api_key = os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY not found in environment variables")

        # Configure Gemini
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('models/gemini-2.5-flash')

        # Simple conversation history storage
        self.conversation_history = []

    def _build_prompt(self, message, conversation_history=None, system_prompt=None):
        """Build prompt with conversation history"""
        if system_prompt is None:
            system_prompt = "You are a helpful AI assistant. Provide detailed, helpful responses."

        prompt_parts = [system_prompt]

        if conversation_history:
            for msg in conversation_history[-10:]:  # Last 10 messages
                if msg['role'] == 'user':
                    prompt_parts.append(f"Human: {msg['content']}")
                elif msg['role'] == 'assistant':
                    prompt_parts.append(f"Assistant: {msg['content']}")

        prompt_parts.append(f"Human: {message}")
        prompt_parts.append("Assistant:")

        return "\n\n".join(prompt_parts)

    def generate_response(self, message, conversation_history=None, system_prompt=None):
        """Generate response using Gemini"""
        try:
            prompt = self._build_prompt(message, conversation_history, system_prompt)
            response = self.model.generate_content(prompt)
            return response.text.strip()

        except Exception as e:
            raise Exception(f"Error generating response: {str(e)}")

    def stream_response(self, message, conversation_history=None, system_prompt=None):
        """Stream response using Gemini"""
        try:
            prompt = self._build_prompt(message, conversation_history, system_prompt)
            response = self.model.generate_content(prompt, stream=True)

            for chunk in response:
                if chunk.text:
                    yield chunk.text

        except Exception as e:
            yield f"Error: {str(e)}"

    def get_memory_summary(self):
        """Get summary of current conversation memory"""
        return {"conversation_history": self.conversation_history}

class AdvancedLangChainService(LangChainService):
    def __init__(self):
        super().__init__()
        self.available_tools = ["search", "calculator"]

    def _search_web(self, query):
        """Simple web search simulation"""
        return f"Search results for '{query}': This is a simulated search result. In production, integrate with a real search API."

    def _calculate(self, expression):
        """Simple calculator"""
        try:
            # Basic math operations only for safety
            allowed_chars = set('0123456789+-*/.() ')
            if all(c in allowed_chars for c in expression):
                result = eval(expression)
                return f"Calculation result: {result}"
            else:
                return "Invalid calculation expression"
        except:
            return "Calculation error"

    def run_agent(self, message, conversation_history=None):
        """Run agent with simulated tools"""
        try:
            # Check if message requires tools
            message_lower = message.lower()

            if any(word in message_lower for word in ['search', 'find', 'look up', 'google']):
                # Extract search query
                search_query = message.replace('search', '').replace('find', '').replace('look up', '').strip()
                tool_result = self._search_web(search_query)
                enhanced_message = f"{message}\n\nTool Result: {tool_result}"
            elif any(word in message_lower for word in ['calculate', 'compute', 'math', '+', '-', '*', '/']):
                # Extract calculation
                import re
                calc_match = re.search(r'[\d+\-*/().]+', message)
                if calc_match:
                    tool_result = self._calculate(calc_match.group())
                    enhanced_message = f"{message}\n\nTool Result: {tool_result}"
                else:
                    enhanced_message = message
            else:
                enhanced_message = message

            # Generate response with tool results
            return self.generate_response(enhanced_message, conversation_history)

        except Exception as e:
            return f"Agent error: {str(e)}"