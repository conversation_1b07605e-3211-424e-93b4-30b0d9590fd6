{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LLM\\\\frontend\\\\src\\\\components\\\\ChatInterface.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport Message from './Message';\nimport ToolsPanel from './ToolsPanel';\nimport { chatAPI } from '../services/api';\nimport './ChatInterface.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatInterface = ({\n  conversationId,\n  messages,\n  onNewMessage,\n  onConversationCreate,\n  user\n}) => {\n  _s();\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [useStreaming, setUseStreaming] = useState(false);\n  const [enabledTools, setEnabledTools] = useState(['rag']);\n  const [systemPrompt, setSystemPrompt] = useState('');\n  const messagesEndRef = useRef(null);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  const handleSendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return;\n    const messageToSend = inputMessage.trim();\n    setInputMessage('');\n    setIsLoading(true);\n\n    // Add user message immediately\n    const userMessage = {\n      role: 'user',\n      content: messageToSend,\n      timestamp: new Date().toISOString()\n    };\n    onNewMessage(userMessage);\n    try {\n      const useAgent = enabledTools.includes('agent');\n      const useRAG = enabledTools.includes('rag');\n      if (useStreaming) {\n        await handleStreamingResponse(messageToSend, useRAG);\n      } else if (useAgent) {\n        await handleAgentResponse(messageToSend);\n      } else {\n        await handleRegularResponse(messageToSend, useRAG);\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n      const errorMessage = {\n        role: 'assistant',\n        content: 'Sorry, I encountered an error. Please try again.',\n        timestamp: new Date().toISOString()\n      };\n      onNewMessage(errorMessage);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleRegularResponse = async (message, useRAG) => {\n    const response = await chatAPI.sendMessage({\n      user_id: user.id,\n      conversation_id: conversationId,\n      message: message,\n      use_rag: useRAG,\n      use_agent: enabledTools.includes('agent'),\n      system_prompt: systemPrompt || undefined\n    });\n    const assistantMessage = {\n      role: 'assistant',\n      content: response.data.response,\n      timestamp: new Date().toISOString(),\n      metadata: {\n        used_agent: response.data.used_agent,\n        used_rag: response.data.used_rag\n      }\n    };\n    onNewMessage(assistantMessage);\n    if (!conversationId && response.data.conversation_id) {\n      onConversationCreate(response.data.conversation_id);\n    }\n  };\n  const handleAgentResponse = async message => {\n    const response = await chatAPI.sendAgentMessage({\n      user_id: user.id,\n      conversation_id: conversationId,\n      message: message,\n      tools: enabledTools\n    });\n    const assistantMessage = {\n      role: 'assistant',\n      content: response.data.response,\n      timestamp: new Date().toISOString(),\n      metadata: {\n        used_agent: true,\n        tools_used: response.data.tools_used\n      }\n    };\n    onNewMessage(assistantMessage);\n    if (!conversationId && response.data.conversation_id) {\n      onConversationCreate(response.data.conversation_id);\n    }\n  };\n  const handleStreamingResponse = async (message, useRAG) => {\n    let fullResponse = '';\n    const assistantMessage = {\n      role: 'assistant',\n      content: '',\n      timestamp: new Date().toISOString(),\n      metadata: {\n        used_rag: useRAG,\n        streaming: true\n      }\n    };\n    onNewMessage(assistantMessage);\n    const messageIndex = messages.length;\n    try {\n      const response = await fetch('http://localhost:5000/api/chat/stream', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          user_id: user.id,\n          conversation_id: conversationId,\n          message: message,\n          use_rag: useRAG\n        })\n      });\n      const reader = response.body.getReader();\n      const decoder = new TextDecoder();\n      while (true) {\n        const {\n          done,\n          value\n        } = await reader.read();\n        if (done) break;\n        const chunk = decoder.decode(value);\n        const lines = chunk.split('\\n');\n        for (const line of lines) {\n          if (line.startsWith('data: ')) {\n            const data = JSON.parse(line.slice(6));\n            if (data.chunk) {\n              fullResponse += data.chunk;\n              onNewMessage({\n                ...assistantMessage,\n                content: fullResponse\n              }, messageIndex);\n            }\n            if (data.complete) {\n              if (!conversationId) {\n                // Handle new conversation creation if needed\n              }\n              return;\n            }\n          }\n        }\n      }\n    } catch (error) {\n      console.error('Streaming error:', error);\n      onNewMessage({\n        ...assistantMessage,\n        content: 'Error: Failed to get streaming response.'\n      }, messageIndex);\n    }\n  };\n  const handleKeyDown = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const handleToolsChange = tools => {\n    setEnabledTools(tools);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"chat-interface\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"header-title\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header-icon\",\n          children: \"\\uD83E\\uDDE0\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Sozhaa Tech AI\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"header-subtitle\",\n          children: \"Powered by Advanced AI\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"chat-controls\",\n        children: /*#__PURE__*/_jsxDEV(\"label\", {\n          children: [/*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"checkbox\",\n            checked: useStreaming,\n            onChange: e => setUseStreaming(e.target.checked)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), \"Use Streaming\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ToolsPanel, {\n      onToolsChange: handleToolsChange,\n      enabledTools: enabledTools\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"system-prompt-input\",\n      children: /*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"text\",\n        value: systemPrompt,\n        onChange: e => setSystemPrompt(e.target.value),\n        placeholder: \"System prompt (optional)...\",\n        className: \"system-prompt-field\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 224,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 223,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"messages-container\",\n      children: [messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"empty-state\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"welcome-icon\",\n          children: \"\\uD83D\\uDE80\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Welcome to Sozhaa Tech AI\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Your intelligent conversation partner powered by advanced AI technology.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"features-list\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\uD83E\\uDD16 LangChain Powered\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Advanced AI with memory and tools\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\uD83D\\uDD0D RAG Enabled\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Search your knowledge base\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n              children: \"\\uD83D\\uDEE0\\uFE0F Agent Tools\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 249,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: \"Web search, calculations, and more\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 235,\n        columnNumber: 11\n      }, this) : messages.map((message, index) => /*#__PURE__*/_jsxDEV(Message, {\n        message: message,\n        isUser: message.role === 'user'\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 13\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), isLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading-indicator\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"typing-dots\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 13\n        }, this), enabledTools.includes('agent') && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"agent-thinking\",\n          children: \"Agent is thinking and using tools...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 272,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 233,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"input-area\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"input-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: inputMessage,\n          onChange: e => setInputMessage(e.target.value),\n          onKeyDown: handleKeyDown,\n          placeholder: \"Type your message here...\",\n          rows: \"1\",\n          disabled: isLoading\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleSendMessage,\n          disabled: isLoading || !inputMessage.trim(),\n          children: \"Send\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"active-tools\",\n        children: [enabledTools.includes('rag') && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tool-tag\",\n          children: \"Knowledge Base\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 44\n        }, this), enabledTools.includes('agent') && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tool-tag\",\n          children: \"AI Agent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 46\n        }, this), useStreaming && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tool-tag\",\n          children: \"Streaming\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 28\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 199,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatInterface, \"Q16r0jDFxhp3qKSpaK9TbTauc+A=\");\n_c = ChatInterface;\nexport default ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Message", "ToolsPanel", "chatAPI", "jsxDEV", "_jsxDEV", "ChatInterface", "conversationId", "messages", "onNewMessage", "onConversationCreate", "user", "_s", "inputMessage", "setInputMessage", "isLoading", "setIsLoading", "useStreaming", "setUseStreaming", "enabledTools", "setEnabledTools", "systemPrompt", "setSystemPrompt", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "trim", "messageToSend", "userMessage", "role", "content", "timestamp", "Date", "toISOString", "useAgent", "includes", "useRAG", "handleStreamingResponse", "handleAgentResponse", "handleRegularResponse", "error", "console", "errorMessage", "message", "response", "sendMessage", "user_id", "id", "conversation_id", "use_rag", "use_agent", "system_prompt", "undefined", "assistant<PERSON><PERSON><PERSON>", "data", "metadata", "used_agent", "used_rag", "sendAgentMessage", "tools", "tools_used", "fullResponse", "streaming", "messageIndex", "length", "fetch", "method", "headers", "body", "JSON", "stringify", "reader", "<PERSON><PERSON><PERSON><PERSON>", "decoder", "TextDecoder", "done", "value", "read", "chunk", "decode", "lines", "split", "line", "startsWith", "parse", "slice", "complete", "handleKeyDown", "e", "key", "shift<PERSON>ey", "preventDefault", "handleToolsChange", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "checked", "onChange", "target", "onToolsChange", "placeholder", "map", "index", "isUser", "ref", "onKeyDown", "rows", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LLM/frontend/src/components/ChatInterface.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport Message from './Message';\r\nimport ToolsPanel from './ToolsPanel';\r\nimport { chatAPI } from '../services/api';\r\nimport './ChatInterface.css';\r\n\r\nconst ChatInterface = ({ \r\n  conversationId, \r\n  messages, \r\n  onNewMessage, \r\n  onConversationCreate,\r\n  user \r\n}) => {\r\n  const [inputMessage, setInputMessage] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [useStreaming, setUseStreaming] = useState(false);\r\n  const [enabledTools, setEnabledTools] = useState(['rag']);\r\n  const [systemPrompt, setSystemPrompt] = useState('');\r\n  const messagesEndRef = useRef(null);\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages]);\r\n\r\n  const handleSendMessage = async () => {\r\n    if (!inputMessage.trim() || isLoading) return;\r\n\r\n    const messageToSend = inputMessage.trim();\r\n    setInputMessage('');\r\n    setIsLoading(true);\r\n\r\n    // Add user message immediately\r\n    const userMessage = {\r\n      role: 'user',\r\n      content: messageToSend,\r\n      timestamp: new Date().toISOString()\r\n    };\r\n    onNewMessage(userMessage);\r\n\r\n    try {\r\n      const useAgent = enabledTools.includes('agent');\r\n      const useRAG = enabledTools.includes('rag');\r\n\r\n      if (useStreaming) {\r\n        await handleStreamingResponse(messageToSend, useRAG);\r\n      } else if (useAgent) {\r\n        await handleAgentResponse(messageToSend);\r\n      } else {\r\n        await handleRegularResponse(messageToSend, useRAG);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error sending message:', error);\r\n      const errorMessage = {\r\n        role: 'assistant',\r\n        content: 'Sorry, I encountered an error. Please try again.',\r\n        timestamp: new Date().toISOString()\r\n      };\r\n      onNewMessage(errorMessage);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleRegularResponse = async (message, useRAG) => {\r\n    const response = await chatAPI.sendMessage({\r\n      user_id: user.id,\r\n      conversation_id: conversationId,\r\n      message: message,\r\n      use_rag: useRAG,\r\n      use_agent: enabledTools.includes('agent'),\r\n      system_prompt: systemPrompt || undefined\r\n    });\r\n\r\n    const assistantMessage = {\r\n      role: 'assistant',\r\n      content: response.data.response,\r\n      timestamp: new Date().toISOString(),\r\n      metadata: {\r\n        used_agent: response.data.used_agent,\r\n        used_rag: response.data.used_rag\r\n      }\r\n    };\r\n    onNewMessage(assistantMessage);\r\n\r\n    if (!conversationId && response.data.conversation_id) {\r\n      onConversationCreate(response.data.conversation_id);\r\n    }\r\n  };\r\n\r\n  const handleAgentResponse = async (message) => {\r\n    const response = await chatAPI.sendAgentMessage({\r\n      user_id: user.id,\r\n      conversation_id: conversationId,\r\n      message: message,\r\n      tools: enabledTools\r\n    });\r\n\r\n    const assistantMessage = {\r\n      role: 'assistant',\r\n      content: response.data.response,\r\n      timestamp: new Date().toISOString(),\r\n      metadata: {\r\n        used_agent: true,\r\n        tools_used: response.data.tools_used\r\n      }\r\n    };\r\n    onNewMessage(assistantMessage);\r\n\r\n    if (!conversationId && response.data.conversation_id) {\r\n      onConversationCreate(response.data.conversation_id);\r\n    }\r\n  };\r\n\r\n  const handleStreamingResponse = async (message, useRAG) => {\r\n    let fullResponse = '';\r\n    const assistantMessage = {\r\n      role: 'assistant',\r\n      content: '',\r\n      timestamp: new Date().toISOString(),\r\n      metadata: {\r\n        used_rag: useRAG,\r\n        streaming: true\r\n      }\r\n    };\r\n    \r\n    onNewMessage(assistantMessage);\r\n    const messageIndex = messages.length;\r\n\r\n    try {\r\n      const response = await fetch('http://localhost:5000/api/chat/stream', {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          user_id: user.id,\r\n          conversation_id: conversationId,\r\n          message: message,\r\n          use_rag: useRAG,\r\n        }),\r\n      });\r\n\r\n      const reader = response.body.getReader();\r\n      const decoder = new TextDecoder();\r\n\r\n      while (true) {\r\n        const { done, value } = await reader.read();\r\n        if (done) break;\r\n\r\n        const chunk = decoder.decode(value);\r\n        const lines = chunk.split('\\n');\r\n\r\n        for (const line of lines) {\r\n          if (line.startsWith('data: ')) {\r\n            const data = JSON.parse(line.slice(6));\r\n            \r\n            if (data.chunk) {\r\n              fullResponse += data.chunk;\r\n              onNewMessage({\r\n                ...assistantMessage,\r\n                content: fullResponse\r\n              }, messageIndex);\r\n            }\r\n            \r\n            if (data.complete) {\r\n              if (!conversationId) {\r\n                // Handle new conversation creation if needed\r\n              }\r\n              return;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Streaming error:', error);\r\n      onNewMessage({\r\n        ...assistantMessage,\r\n        content: 'Error: Failed to get streaming response.'\r\n      }, messageIndex);\r\n    }\r\n  };\r\n\r\n  const handleKeyDown = (e) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleSendMessage();\r\n    }\r\n  };\r\n\r\n  const handleToolsChange = (tools) => {\r\n    setEnabledTools(tools);\r\n  };\r\n\r\n  return (\r\n    <div className=\"chat-interface\">\r\n      <div className=\"chat-header\">\r\n        <div className=\"header-title\">\r\n          <span className=\"header-icon\">🧠</span>\r\n          <h3>Sozhaa Tech AI</h3>\r\n          <span className=\"header-subtitle\">Powered by Advanced AI</span>\r\n        </div>\r\n        <div className=\"chat-controls\">\r\n          <label>\r\n            <input\r\n              type=\"checkbox\"\r\n              checked={useStreaming}\r\n              onChange={(e) => setUseStreaming(e.target.checked)}\r\n            />\r\n            Use Streaming\r\n          </label>\r\n        </div>\r\n      </div>\r\n\r\n      <ToolsPanel \r\n        onToolsChange={handleToolsChange}\r\n        enabledTools={enabledTools}\r\n      />\r\n\r\n      <div className=\"system-prompt-input\">\r\n        <input\r\n          type=\"text\"\r\n          value={systemPrompt}\r\n          onChange={(e) => setSystemPrompt(e.target.value)}\r\n          placeholder=\"System prompt (optional)...\"\r\n          className=\"system-prompt-field\"\r\n        />\r\n      </div>\r\n\r\n      <div className=\"messages-container\">\r\n        {messages.length === 0 ? (\r\n          <div className=\"empty-state\">\r\n            <div className=\"welcome-icon\">🚀</div>\r\n            <h2>Welcome to Sozhaa Tech AI</h2>\r\n            <p>Your intelligent conversation partner powered by advanced AI technology.</p>\r\n            <div className=\"features-list\">\r\n              <div className=\"feature-item\">\r\n                <strong>🤖 LangChain Powered</strong>\r\n                <span>Advanced AI with memory and tools</span>\r\n              </div>\r\n              <div className=\"feature-item\">\r\n                <strong>🔍 RAG Enabled</strong>\r\n                <span>Search your knowledge base</span>\r\n              </div>\r\n              <div className=\"feature-item\">\r\n                <strong>🛠️ Agent Tools</strong>\r\n                <span>Web search, calculations, and more</span>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          messages.map((message, index) => (\r\n            <Message\r\n              key={index}\r\n              message={message}\r\n              isUser={message.role === 'user'}\r\n            />\r\n          ))\r\n        )}\r\n        <div ref={messagesEndRef} />\r\n        {isLoading && (\r\n          <div className=\"loading-indicator\">\r\n            <div className=\"typing-dots\">\r\n              <span></span>\r\n              <span></span>\r\n              <span></span>\r\n            </div>\r\n            {enabledTools.includes('agent') && (\r\n              <div className=\"agent-thinking\">Agent is thinking and using tools...</div>\r\n            )}\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"input-area\">\r\n        <div className=\"input-container\">\r\n          <textarea\r\n            value={inputMessage}\r\n            onChange={(e) => setInputMessage(e.target.value)}\r\n            onKeyDown={handleKeyDown}\r\n            placeholder=\"Type your message here...\"\r\n            rows=\"1\"\r\n            disabled={isLoading}\r\n          />\r\n          <button\r\n            onClick={handleSendMessage}\r\n            disabled={isLoading || !inputMessage.trim()}\r\n          >\r\n            Send\r\n          </button>\r\n        </div>\r\n        <div className=\"active-tools\">\r\n          {enabledTools.includes('rag') && <span className=\"tool-tag\">Knowledge Base</span>}\r\n          {enabledTools.includes('agent') && <span className=\"tool-tag\">AI Agent</span>}\r\n          {useStreaming && <span className=\"tool-tag\">Streaming</span>}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatInterface;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,OAAO,QAAQ,iBAAiB;AACzC,OAAO,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,aAAa,GAAGA,CAAC;EACrBC,cAAc;EACdC,QAAQ;EACRC,YAAY;EACZC,oBAAoB;EACpBC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqB,YAAY,EAAEC,eAAe,CAAC,GAAGtB,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC;EACzD,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAMyB,cAAc,GAAGxB,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAMyB,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAED5B,SAAS,CAAC,MAAM;IACdwB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAChB,QAAQ,CAAC,CAAC;EAEd,MAAMqB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAAChB,YAAY,CAACiB,IAAI,CAAC,CAAC,IAAIf,SAAS,EAAE;IAEvC,MAAMgB,aAAa,GAAGlB,YAAY,CAACiB,IAAI,CAAC,CAAC;IACzChB,eAAe,CAAC,EAAE,CAAC;IACnBE,YAAY,CAAC,IAAI,CAAC;;IAElB;IACA,MAAMgB,WAAW,GAAG;MAClBC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAEH,aAAa;MACtBI,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IACpC,CAAC;IACD5B,YAAY,CAACuB,WAAW,CAAC;IAEzB,IAAI;MACF,MAAMM,QAAQ,GAAGnB,YAAY,CAACoB,QAAQ,CAAC,OAAO,CAAC;MAC/C,MAAMC,MAAM,GAAGrB,YAAY,CAACoB,QAAQ,CAAC,KAAK,CAAC;MAE3C,IAAItB,YAAY,EAAE;QAChB,MAAMwB,uBAAuB,CAACV,aAAa,EAAES,MAAM,CAAC;MACtD,CAAC,MAAM,IAAIF,QAAQ,EAAE;QACnB,MAAMI,mBAAmB,CAACX,aAAa,CAAC;MAC1C,CAAC,MAAM;QACL,MAAMY,qBAAqB,CAACZ,aAAa,EAAES,MAAM,CAAC;MACpD;IACF,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,MAAME,YAAY,GAAG;QACnBb,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE,kDAAkD;QAC3DC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;MACD5B,YAAY,CAACqC,YAAY,CAAC;IAC5B,CAAC,SAAS;MACR9B,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM2B,qBAAqB,GAAG,MAAAA,CAAOI,OAAO,EAAEP,MAAM,KAAK;IACvD,MAAMQ,QAAQ,GAAG,MAAM7C,OAAO,CAAC8C,WAAW,CAAC;MACzCC,OAAO,EAAEvC,IAAI,CAACwC,EAAE;MAChBC,eAAe,EAAE7C,cAAc;MAC/BwC,OAAO,EAAEA,OAAO;MAChBM,OAAO,EAAEb,MAAM;MACfc,SAAS,EAAEnC,YAAY,CAACoB,QAAQ,CAAC,OAAO,CAAC;MACzCgB,aAAa,EAAElC,YAAY,IAAImC;IACjC,CAAC,CAAC;IAEF,MAAMC,gBAAgB,GAAG;MACvBxB,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAEc,QAAQ,CAACU,IAAI,CAACV,QAAQ;MAC/Bb,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCsB,QAAQ,EAAE;QACRC,UAAU,EAAEZ,QAAQ,CAACU,IAAI,CAACE,UAAU;QACpCC,QAAQ,EAAEb,QAAQ,CAACU,IAAI,CAACG;MAC1B;IACF,CAAC;IACDpD,YAAY,CAACgD,gBAAgB,CAAC;IAE9B,IAAI,CAAClD,cAAc,IAAIyC,QAAQ,CAACU,IAAI,CAACN,eAAe,EAAE;MACpD1C,oBAAoB,CAACsC,QAAQ,CAACU,IAAI,CAACN,eAAe,CAAC;IACrD;EACF,CAAC;EAED,MAAMV,mBAAmB,GAAG,MAAOK,OAAO,IAAK;IAC7C,MAAMC,QAAQ,GAAG,MAAM7C,OAAO,CAAC2D,gBAAgB,CAAC;MAC9CZ,OAAO,EAAEvC,IAAI,CAACwC,EAAE;MAChBC,eAAe,EAAE7C,cAAc;MAC/BwC,OAAO,EAAEA,OAAO;MAChBgB,KAAK,EAAE5C;IACT,CAAC,CAAC;IAEF,MAAMsC,gBAAgB,GAAG;MACvBxB,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAEc,QAAQ,CAACU,IAAI,CAACV,QAAQ;MAC/Bb,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCsB,QAAQ,EAAE;QACRC,UAAU,EAAE,IAAI;QAChBI,UAAU,EAAEhB,QAAQ,CAACU,IAAI,CAACM;MAC5B;IACF,CAAC;IACDvD,YAAY,CAACgD,gBAAgB,CAAC;IAE9B,IAAI,CAAClD,cAAc,IAAIyC,QAAQ,CAACU,IAAI,CAACN,eAAe,EAAE;MACpD1C,oBAAoB,CAACsC,QAAQ,CAACU,IAAI,CAACN,eAAe,CAAC;IACrD;EACF,CAAC;EAED,MAAMX,uBAAuB,GAAG,MAAAA,CAAOM,OAAO,EAAEP,MAAM,KAAK;IACzD,IAAIyB,YAAY,GAAG,EAAE;IACrB,MAAMR,gBAAgB,GAAG;MACvBxB,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAE,EAAE;MACXC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;MACnCsB,QAAQ,EAAE;QACRE,QAAQ,EAAErB,MAAM;QAChB0B,SAAS,EAAE;MACb;IACF,CAAC;IAEDzD,YAAY,CAACgD,gBAAgB,CAAC;IAC9B,MAAMU,YAAY,GAAG3D,QAAQ,CAAC4D,MAAM;IAEpC,IAAI;MACF,MAAMpB,QAAQ,GAAG,MAAMqB,KAAK,CAAC,uCAAuC,EAAE;QACpEC,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBxB,OAAO,EAAEvC,IAAI,CAACwC,EAAE;UAChBC,eAAe,EAAE7C,cAAc;UAC/BwC,OAAO,EAAEA,OAAO;UAChBM,OAAO,EAAEb;QACX,CAAC;MACH,CAAC,CAAC;MAEF,MAAMmC,MAAM,GAAG3B,QAAQ,CAACwB,IAAI,CAACI,SAAS,CAAC,CAAC;MACxC,MAAMC,OAAO,GAAG,IAAIC,WAAW,CAAC,CAAC;MAEjC,OAAO,IAAI,EAAE;QACX,MAAM;UAAEC,IAAI;UAAEC;QAAM,CAAC,GAAG,MAAML,MAAM,CAACM,IAAI,CAAC,CAAC;QAC3C,IAAIF,IAAI,EAAE;QAEV,MAAMG,KAAK,GAAGL,OAAO,CAACM,MAAM,CAACH,KAAK,CAAC;QACnC,MAAMI,KAAK,GAAGF,KAAK,CAACG,KAAK,CAAC,IAAI,CAAC;QAE/B,KAAK,MAAMC,IAAI,IAAIF,KAAK,EAAE;UACxB,IAAIE,IAAI,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC7B,MAAM7B,IAAI,GAAGe,IAAI,CAACe,KAAK,CAACF,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC,CAAC;YAEtC,IAAI/B,IAAI,CAACwB,KAAK,EAAE;cACdjB,YAAY,IAAIP,IAAI,CAACwB,KAAK;cAC1BzE,YAAY,CAAC;gBACX,GAAGgD,gBAAgB;gBACnBvB,OAAO,EAAE+B;cACX,CAAC,EAAEE,YAAY,CAAC;YAClB;YAEA,IAAIT,IAAI,CAACgC,QAAQ,EAAE;cACjB,IAAI,CAACnF,cAAc,EAAE;gBACnB;cAAA;cAEF;YACF;UACF;QACF;MACF;IACF,CAAC,CAAC,OAAOqC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkB,EAAEA,KAAK,CAAC;MACxCnC,YAAY,CAAC;QACX,GAAGgD,gBAAgB;QACnBvB,OAAO,EAAE;MACX,CAAC,EAAEiC,YAAY,CAAC;IAClB;EACF,CAAC;EAED,MAAMwB,aAAa,GAAIC,CAAC,IAAK;IAC3B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBlE,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMmE,iBAAiB,GAAIjC,KAAK,IAAK;IACnC3C,eAAe,CAAC2C,KAAK,CAAC;EACxB,CAAC;EAED,oBACE1D,OAAA;IAAK4F,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7B7F,OAAA;MAAK4F,SAAS,EAAC,aAAa;MAAAC,QAAA,gBAC1B7F,OAAA;QAAK4F,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3B7F,OAAA;UAAM4F,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvCjG,OAAA;UAAA6F,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvBjG,OAAA;UAAM4F,SAAS,EAAC,iBAAiB;UAAAC,QAAA,EAAC;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5D,CAAC,eACNjG,OAAA;QAAK4F,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5B7F,OAAA;UAAA6F,QAAA,gBACE7F,OAAA;YACEkG,IAAI,EAAC,UAAU;YACfC,OAAO,EAAEvF,YAAa;YACtBwF,QAAQ,EAAGb,CAAC,IAAK1E,eAAe,CAAC0E,CAAC,CAACc,MAAM,CAACF,OAAO;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,iBAEJ;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENjG,OAAA,CAACH,UAAU;MACTyG,aAAa,EAAEX,iBAAkB;MACjC7E,YAAY,EAAEA;IAAa;MAAAgF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAEFjG,OAAA;MAAK4F,SAAS,EAAC,qBAAqB;MAAAC,QAAA,eAClC7F,OAAA;QACEkG,IAAI,EAAC,MAAM;QACXvB,KAAK,EAAE3D,YAAa;QACpBoF,QAAQ,EAAGb,CAAC,IAAKtE,eAAe,CAACsE,CAAC,CAACc,MAAM,CAAC1B,KAAK,CAAE;QACjD4B,WAAW,EAAC,6BAA6B;QACzCX,SAAS,EAAC;MAAqB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENjG,OAAA;MAAK4F,SAAS,EAAC,oBAAoB;MAAAC,QAAA,GAChC1F,QAAQ,CAAC4D,MAAM,KAAK,CAAC,gBACpB/D,OAAA;QAAK4F,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B7F,OAAA;UAAK4F,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtCjG,OAAA;UAAA6F,QAAA,EAAI;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClCjG,OAAA;UAAA6F,QAAA,EAAG;QAAwE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/EjG,OAAA;UAAK4F,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5B7F,OAAA;YAAK4F,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B7F,OAAA;cAAA6F,QAAA,EAAQ;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACrCjG,OAAA;cAAA6F,QAAA,EAAM;YAAiC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACNjG,OAAA;YAAK4F,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B7F,OAAA;cAAA6F,QAAA,EAAQ;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAC/BjG,OAAA;cAAA6F,QAAA,EAAM;YAA0B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC,eACNjG,OAAA;YAAK4F,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B7F,OAAA;cAAA6F,QAAA,EAAQ;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChCjG,OAAA;cAAA6F,QAAA,EAAM;YAAkC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,GAEN9F,QAAQ,CAACqG,GAAG,CAAC,CAAC9D,OAAO,EAAE+D,KAAK,kBAC1BzG,OAAA,CAACJ,OAAO;QAEN8C,OAAO,EAAEA,OAAQ;QACjBgE,MAAM,EAAEhE,OAAO,CAACd,IAAI,KAAK;MAAO,GAF3B6E,KAAK;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAGX,CACF,CACF,eACDjG,OAAA;QAAK2G,GAAG,EAAEzF;MAAe;QAAA4E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAC3BvF,SAAS,iBACRV,OAAA;QAAK4F,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChC7F,OAAA;UAAK4F,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B7F,OAAA;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjG,OAAA;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbjG,OAAA;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,EACLnF,YAAY,CAACoB,QAAQ,CAAC,OAAO,CAAC,iBAC7BlC,OAAA;UAAK4F,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAC1E;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENjG,OAAA;MAAK4F,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzB7F,OAAA;QAAK4F,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9B7F,OAAA;UACE2E,KAAK,EAAEnE,YAAa;UACpB4F,QAAQ,EAAGb,CAAC,IAAK9E,eAAe,CAAC8E,CAAC,CAACc,MAAM,CAAC1B,KAAK,CAAE;UACjDiC,SAAS,EAAEtB,aAAc;UACzBiB,WAAW,EAAC,2BAA2B;UACvCM,IAAI,EAAC,GAAG;UACRC,QAAQ,EAAEpG;QAAU;UAAAoF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eACFjG,OAAA;UACE+G,OAAO,EAAEvF,iBAAkB;UAC3BsF,QAAQ,EAAEpG,SAAS,IAAI,CAACF,YAAY,CAACiB,IAAI,CAAC,CAAE;UAAAoE,QAAA,EAC7C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eACNjG,OAAA;QAAK4F,SAAS,EAAC,cAAc;QAAAC,QAAA,GAC1B/E,YAAY,CAACoB,QAAQ,CAAC,KAAK,CAAC,iBAAIlC,OAAA;UAAM4F,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAChFnF,YAAY,CAACoB,QAAQ,CAAC,OAAO,CAAC,iBAAIlC,OAAA;UAAM4F,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAC5ErF,YAAY,iBAAIZ,OAAA;UAAM4F,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAS;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1F,EAAA,CAxSIN,aAAa;AAAA+G,EAAA,GAAb/G,aAAa;AA0SnB,eAAeA,aAAa;AAAC,IAAA+G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}