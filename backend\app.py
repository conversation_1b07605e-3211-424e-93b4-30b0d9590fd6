from flask import Flask, request, jsonify, Response
from flask_cors import CORS
from models.database import db
from services.langchain_service import LangChainService, AdvancedLangChainService
from services.memory_service import MemoryService
from services.rag_service import RAGService
from services.agent_service import AgentService
from services.auth_service import AuthService
import os
import uuid
import json
from dotenv import load_dotenv

load_dotenv()

app = Flask(__name__)
CORS(app)

# Configuration
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv('DATABASE_URL')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SECRET_KEY'] = os.getenv('SECRET_KEY')

# Initialize extensions
db.init_app(app)

# Initialize services
langchain_service = LangChainService()
advanced_langchain_service = AdvancedLangChainService()
memory_service = MemoryService()
rag_service = RAGService()
agent_service = AgentService()
auth_service = AuthService()

# Create tables
with app.app_context():
    db.create_all()

@app.route('/api/chat', methods=['POST'])
def chat():
    """Handle chat messages with LangChain"""
    try:
        data = request.json
        user_id = data.get('user_id')
        conversation_id = data.get('conversation_id')
        message = data.get('message')
        use_rag = data.get('use_rag', False)
        use_agent = data.get('use_agent', False)
        system_prompt = data.get('system_prompt')
        
        if not user_id or not message:
            return jsonify({'error': 'Missing required fields'}), 400
        
        # Validate user
        if not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401
        
        # Create conversation if doesn't exist
        if not conversation_id:
            conversation_id = memory_service.create_conversation(user_id, message[:50])
        
        # Add user message to memory
        memory_service.add_message(conversation_id, 'user', message)
        
        # Get conversation history
        history = memory_service.get_conversation_history(conversation_id)
        
        # Choose service based on flags
        if use_agent:
            response = agent_service.run_enhanced_agent(message, history, use_rag)
        elif use_rag:
            # Enhanced RAG with LangChain
            context = rag_service.get_context(message)
            enhanced_prompt = f"Context: {context}\n\nUser Question: {message}"
            response = langchain_service.generate_response(enhanced_prompt, history, system_prompt)
        else:
            response = langchain_service.generate_response(message, history, system_prompt)
        
        # Add assistant response to memory
        memory_service.add_message(conversation_id, 'assistant', response)
        
        return jsonify({
            'response': response,
            'conversation_id': conversation_id,
            'used_agent': use_agent,
            'used_rag': use_rag
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/chat/stream', methods=['POST'])
def chat_stream():
    """Stream chat response with LangChain"""
    try:
        data = request.json
        user_id = data.get('user_id')
        conversation_id = data.get('conversation_id')
        message = data.get('message')
        use_rag = data.get('use_rag', False)
        
        if not user_id or not message:
            return jsonify({'error': 'Missing required fields'}), 400
        
        # Validate user
        if not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401
        
        # Create conversation if doesn't exist
        if not conversation_id:
            conversation_id = memory_service.create_conversation(user_id, message[:50])
        
        # Add user message to memory
        memory_service.add_message(conversation_id, 'user', message)
        
        # Get conversation history
        history = memory_service.get_conversation_history(conversation_id)
        
        def generate():
            full_response = ""
            
            if use_rag:
                context = rag_service.get_context(message)
                enhanced_prompt = f"Context: {context}\n\nUser Question: {message}"
                stream = langchain_service.stream_response(enhanced_prompt, history)
            else:
                stream = langchain_service.stream_response(message, history)
            
            for chunk in stream:
                full_response += chunk
                yield f"data: {json.dumps({'chunk': chunk})}\n\n"
            
            # Add final response to memory
            memory_service.add_message(conversation_id, 'assistant', full_response)
            yield f"data: {json.dumps({'complete': True})}\n\n"
        
        return Response(generate(), mimetype='text/plain')
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/chat/agent', methods=['POST'])
def chat_agent():
    """Chat using LangChain agent with tools"""
    try:
        data = request.json
        user_id = data.get('user_id')
        conversation_id = data.get('conversation_id')
        message = data.get('message')
        tools = data.get('tools', [])  # Specific tools to use
        
        if not user_id or not message:
            return jsonify({'error': 'Missing required fields'}), 400
        
        # Validate user
        if not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401
        
        # Create conversation if doesn't exist
        if not conversation_id:
            conversation_id = memory_service.create_conversation(user_id, message[:50])
        
        # Add user message to memory
        memory_service.add_message(conversation_id, 'user', message)
        
        # Get conversation history
        history = memory_service.get_conversation_history(conversation_id)
        
        # Run agent
        response = agent_service.run_enhanced_agent(message, history, use_rag=True)
        
        # Add assistant response to memory
        memory_service.add_message(conversation_id, 'assistant', response)
        
        return jsonify({
            'response': response,
            'conversation_id': conversation_id,
            'used_agent': True,
            'tools_used': tools
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/agent/tools', methods=['GET'])
def get_agent_tools():
    """Get available agent tools"""
    try:
        tools = agent_service.get_available_tools()
        return jsonify({'tools': tools})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/login', methods=['POST'])
def login():
    """Handle user login/registration"""
    try:
        data = request.json
        username = data.get('username')
        email = data.get('email')

        if not username or not email:
            return jsonify({'error': 'Username and email are required'}), 400

        # Authenticate or create user
        user_id = auth_service.authenticate_user(username, email)

        return jsonify({
            'success': True,
            'user_id': user_id,
            'username': username,
            'email': email
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/auth/validate', methods=['POST'])
def validate_user():
    """Validate user session"""
    try:
        data = request.json
        user_id = data.get('user_id')

        if not user_id:
            return jsonify({'error': 'User ID required'}), 400

        is_valid = auth_service.validate_user(user_id)

        return jsonify({
            'valid': is_valid
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/conversations', methods=['GET'])
def get_conversations():
    """Get user conversations"""
    try:
        user_id = request.args.get('user_id')

        if not user_id:
            return jsonify({'error': 'User ID required'}), 400

        # Validate user
        if not auth_service.validate_user(user_id):
            return jsonify({'error': 'Invalid user'}), 401

        conversations = memory_service.get_user_conversations(user_id)

        return jsonify({
            'conversations': conversations
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/conversation/<conversation_id>', methods=['GET'])
def get_conversation(conversation_id):
    """Get conversation messages"""
    try:
        messages = memory_service.get_conversation_history(conversation_id)

        return jsonify({
            'messages': messages
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/langchain/memory', methods=['GET'])
def get_langchain_memory():
    """Get LangChain memory state"""
    try:
        memory_state = langchain_service.get_memory_summary()
        return jsonify({'memory': memory_state})
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/rag/advanced', methods=['POST'])
def rag_advanced():
    """Advanced RAG with LangChain"""
    try:
        data = request.json
        query = data.get('query')
        user_id = data.get('user_id')
        
        if not query or not user_id:
            return jsonify({'error': 'Query and user_id required'}), 400
        
        # Use LangChain service for the response
        response = langchain_service.generate_response(
            query, 
            system_prompt="Use the provided context to answer the question accurately."
        )
        
        return jsonify({
            'response': response,
            'query': query
        })
    
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, port=5000)