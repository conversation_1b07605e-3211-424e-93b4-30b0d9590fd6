{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LLM\\\\frontend\\\\src\\\\components\\\\ToolsPanel.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { agentAPI } from '../services/api';\nimport './ToolsPanel.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ToolsPanel = ({\n  onToolsChange,\n  enabledTools\n}) => {\n  _s();\n  const [availableTools, setAvailableTools] = useState([]);\n  const [isLoading, setIsLoading] = useState(false);\n  useEffect(() => {\n    loadAvailableTools();\n  }, []);\n  const loadAvailableTools = async () => {\n    try {\n      setIsLoading(true);\n      const response = await agentAPI.getTools();\n      setAvailableTools(response.data.tools);\n    } catch (error) {\n      console.error('Error loading tools:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const handleToolToggle = toolName => {\n    const newEnabledTools = enabledTools.includes(toolName) ? enabledTools.filter(tool => tool !== toolName) : [...enabledTools, toolName];\n    onToolsChange(newEnabledTools);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"tools-panel\",\n    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n      children: \"AI Tools & Capabilities\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"tools-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"tool-toggle\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: enabledTools.includes('rag'),\n          onChange: () => handleToolToggle('rag')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tool-label\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tool-name\",\n            children: \"Knowledge Base\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tool-description\",\n            children: \"Search internal documents\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        className: \"tool-toggle\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"checkbox\",\n          checked: enabledTools.includes('agent'),\n          onChange: () => handleToolToggle('agent')\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tool-label\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tool-name\",\n            children: \"AI Agent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"tool-description\",\n            children: \"Use tools for complex tasks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"langchain-tools\",\n      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n        children: \"Available Tools\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), isLoading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"loading\",\n        children: \"Loading tools...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 11\n      }, this) : availableTools.map(tool => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tool-item\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"tool-badge\",\n          children: tool\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 15\n        }, this)\n      }, tool, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 13\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"capabilities-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n        children: \"Capabilities\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 77,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\uD83D\\uDCAC Conversational Memory\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\uD83D\\uDD0D Semantic Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\uD83D\\uDEE0\\uFE0F Tool Usage\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 81,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\uD83D\\uDCDA Document Analysis\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\uD83C\\uDF10 Web Search\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 34,\n    columnNumber: 5\n  }, this);\n};\n_s(ToolsPanel, \"CBAxU4nGUHG+OByAY+n5Yju1vsY=\");\n_c = ToolsPanel;\nexport default ToolsPanel;\nvar _c;\n$RefreshReg$(_c, \"ToolsPanel\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "agentAPI", "jsxDEV", "_jsxDEV", "ToolsPanel", "onToolsChange", "enabledTools", "_s", "availableTools", "setAvailableTools", "isLoading", "setIsLoading", "loadAvailableTools", "response", "getTools", "data", "tools", "error", "console", "handleToolToggle", "toolName", "newEnabledTools", "includes", "filter", "tool", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "checked", "onChange", "map", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LLM/frontend/src/components/ToolsPanel.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { agentAPI } from '../services/api';\r\nimport './ToolsPanel.css';\r\n\r\nconst ToolsPanel = ({ onToolsChange, enabledTools }) => {\r\n  const [availableTools, setAvailableTools] = useState([]);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n\r\n  useEffect(() => {\r\n    loadAvailableTools();\r\n  }, []);\r\n\r\n  const loadAvailableTools = async () => {\r\n    try {\r\n      setIsLoading(true);\r\n      const response = await agentAPI.getTools();\r\n      setAvailableTools(response.data.tools);\r\n    } catch (error) {\r\n      console.error('Error loading tools:', error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleToolToggle = (toolName) => {\r\n    const newEnabledTools = enabledTools.includes(toolName)\r\n      ? enabledTools.filter(tool => tool !== toolName)\r\n      : [...enabledTools, toolName];\r\n    \r\n    onToolsChange(newEnabledTools);\r\n  };\r\n\r\n  return (\r\n    <div className=\"tools-panel\">\r\n      <h4>AI Tools & Capabilities</h4>\r\n      \r\n      <div className=\"tools-section\">\r\n        <label className=\"tool-toggle\">\r\n          <input\r\n            type=\"checkbox\"\r\n            checked={enabledTools.includes('rag')}\r\n            onChange={() => handleToolToggle('rag')}\r\n          />\r\n          <span className=\"tool-label\">\r\n            <span className=\"tool-name\">Knowledge Base</span>\r\n            <span className=\"tool-description\">Search internal documents</span>\r\n          </span>\r\n        </label>\r\n\r\n        <label className=\"tool-toggle\">\r\n          <input\r\n            type=\"checkbox\"\r\n            checked={enabledTools.includes('agent')}\r\n            onChange={() => handleToolToggle('agent')}\r\n          />\r\n          <span className=\"tool-label\">\r\n            <span className=\"tool-name\">AI Agent</span>\r\n            <span className=\"tool-description\">Use tools for complex tasks</span>\r\n          </span>\r\n        </label>\r\n      </div>\r\n\r\n      <div className=\"langchain-tools\">\r\n        <h5>Available Tools</h5>\r\n        {isLoading ? (\r\n          <div className=\"loading\">Loading tools...</div>\r\n        ) : (\r\n          availableTools.map(tool => (\r\n            <div key={tool} className=\"tool-item\">\r\n              <span className=\"tool-badge\">{tool}</span>\r\n            </div>\r\n          ))\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"capabilities-info\">\r\n        <h5>Capabilities</h5>\r\n        <ul>\r\n          <li>💬 Conversational Memory</li>\r\n          <li>🔍 Semantic Search</li>\r\n          <li>🛠️ Tool Usage</li>\r\n          <li>📚 Document Analysis</li>\r\n          <li>🌐 Web Search</li>\r\n        </ul>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ToolsPanel;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAU,GAAGA,CAAC;EAAEC,aAAa;EAAEC;AAAa,CAAC,KAAK;EAAAC,EAAA;EACtD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACdY,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,kBAAkB,GAAG,MAAAA,CAAA,KAAY;IACrC,IAAI;MACFD,YAAY,CAAC,IAAI,CAAC;MAClB,MAAME,QAAQ,GAAG,MAAMZ,QAAQ,CAACa,QAAQ,CAAC,CAAC;MAC1CL,iBAAiB,CAACI,QAAQ,CAACE,IAAI,CAACC,KAAK,CAAC;IACxC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,SAAS;MACRN,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMQ,gBAAgB,GAAIC,QAAQ,IAAK;IACrC,MAAMC,eAAe,GAAGf,YAAY,CAACgB,QAAQ,CAACF,QAAQ,CAAC,GACnDd,YAAY,CAACiB,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAKJ,QAAQ,CAAC,GAC9C,CAAC,GAAGd,YAAY,EAAEc,QAAQ,CAAC;IAE/Bf,aAAa,CAACgB,eAAe,CAAC;EAChC,CAAC;EAED,oBACElB,OAAA;IAAKsB,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAC1BvB,OAAA;MAAAuB,QAAA,EAAI;IAAuB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAEhC3B,OAAA;MAAKsB,SAAS,EAAC,eAAe;MAAAC,QAAA,gBAC5BvB,OAAA;QAAOsB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC5BvB,OAAA;UACE4B,IAAI,EAAC,UAAU;UACfC,OAAO,EAAE1B,YAAY,CAACgB,QAAQ,CAAC,KAAK,CAAE;UACtCW,QAAQ,EAAEA,CAAA,KAAMd,gBAAgB,CAAC,KAAK;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eACF3B,OAAA;UAAMsB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAC1BvB,OAAA;YAAMsB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjD3B,OAAA;YAAMsB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAER3B,OAAA;QAAOsB,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC5BvB,OAAA;UACE4B,IAAI,EAAC,UAAU;UACfC,OAAO,EAAE1B,YAAY,CAACgB,QAAQ,CAAC,OAAO,CAAE;UACxCW,QAAQ,EAAEA,CAAA,KAAMd,gBAAgB,CAAC,OAAO;QAAE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eACF3B,OAAA;UAAMsB,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAC1BvB,OAAA;YAAMsB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3C3B,OAAA;YAAMsB,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAEN3B,OAAA;MAAKsB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BvB,OAAA;QAAAuB,QAAA,EAAI;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACvBpB,SAAS,gBACRP,OAAA;QAAKsB,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAgB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,GAE/CtB,cAAc,CAAC0B,GAAG,CAACV,IAAI,iBACrBrB,OAAA;QAAgBsB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACnCvB,OAAA;UAAMsB,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAEF;QAAI;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC,GADlCN,IAAI;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAET,CACN,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN3B,OAAA;MAAKsB,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChCvB,OAAA;QAAAuB,QAAA,EAAI;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACrB3B,OAAA;QAAAuB,QAAA,gBACEvB,OAAA;UAAAuB,QAAA,EAAI;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjC3B,OAAA;UAAAuB,QAAA,EAAI;QAAkB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3B3B,OAAA;UAAAuB,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvB3B,OAAA;UAAAuB,QAAA,EAAI;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7B3B,OAAA;UAAAuB,QAAA,EAAI;QAAa;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvB,EAAA,CAnFIH,UAAU;AAAA+B,EAAA,GAAV/B,UAAU;AAqFhB,eAAeA,UAAU;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}