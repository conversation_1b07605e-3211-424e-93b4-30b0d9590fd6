{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LLM\\\\frontend\\\\src\\\\components\\\\Sidebar.js\";\nimport React from 'react';\nimport './Sidebar.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  conversations,\n  currentConversation,\n  onSelectConversation,\n  onNewConversation,\n  user\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"sidebar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"logo-icon\",\n          children: \"\\uD83E\\uDD16\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Sozhaa Tech AI\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        className: \"new-chat-btn\",\n        onClick: onNewConversation,\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"btn-icon\",\n          children: \"\\u2728\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this), \"New Chat\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"conversations-list\",\n      children: conversations.map(conversation => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `conversation-item ${currentConversation === conversation.id ? 'active' : ''}`,\n        onClick: () => onSelectConversation(conversation.id),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"conversation-title\",\n          children: conversation.title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"conversation-date\",\n          children: new Date(conversation.updated_at).toLocaleDateString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 13\n        }, this)]\n      }, conversation.id, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"sidebar-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"user-info\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-avatar\",\n          children: \"\\uD83D\\uDC64\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-name\",\n          children: user === null || user === void 0 ? void 0 : user.username\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Sidebar", "conversations", "currentConversation", "onSelectConversation", "onNewConversation", "user", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "map", "conversation", "id", "title", "Date", "updated_at", "toLocaleDateString", "username", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LLM/frontend/src/components/Sidebar.js"], "sourcesContent": ["import React from 'react';\r\nimport './Sidebar.css';\r\n\r\nconst Sidebar = ({ \r\n  conversations, \r\n  currentConversation, \r\n  onSelectConversation, \r\n  onNewConversation,\r\n  user \r\n}) => {\r\n  return (\r\n    <div className=\"sidebar\">\r\n      <div className=\"sidebar-header\">\r\n        <div className=\"logo-section\">\r\n          <div className=\"logo-icon\">🤖</div>\r\n          <h2>Sozhaa Tech AI</h2>\r\n        </div>\r\n        <button className=\"new-chat-btn\" onClick={onNewConversation}>\r\n          <span className=\"btn-icon\">✨</span>\r\n          New Chat\r\n        </button>\r\n      </div>\r\n      \r\n      <div className=\"conversations-list\">\r\n        {conversations.map(conversation => (\r\n          <div\r\n            key={conversation.id}\r\n            className={`conversation-item ${\r\n              currentConversation === conversation.id ? 'active' : ''\r\n            }`}\r\n            onClick={() => onSelectConversation(conversation.id)}\r\n          >\r\n            <div className=\"conversation-title\">\r\n              {conversation.title}\r\n            </div>\r\n            <div className=\"conversation-date\">\r\n              {new Date(conversation.updated_at).toLocaleDateString()}\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n      \r\n      <div className=\"sidebar-footer\">\r\n        <div className=\"user-info\">\r\n          <div className=\"user-avatar\">👤</div>\r\n          <div className=\"user-name\">{user?.username}</div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Sidebar;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EACfC,aAAa;EACbC,mBAAmB;EACnBC,oBAAoB;EACpBC,iBAAiB;EACjBC;AACF,CAAC,KAAK;EACJ,oBACEN,OAAA;IAAKO,SAAS,EAAC,SAAS;IAAAC,QAAA,gBACtBR,OAAA;MAAKO,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BR,OAAA;QAAKO,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BR,OAAA;UAAKO,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnCZ,OAAA;UAAAQ,QAAA,EAAI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC,eACNZ,OAAA;QAAQO,SAAS,EAAC,cAAc;QAACM,OAAO,EAAER,iBAAkB;QAAAG,QAAA,gBAC1DR,OAAA;UAAMO,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,YAErC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENZ,OAAA;MAAKO,SAAS,EAAC,oBAAoB;MAAAC,QAAA,EAChCN,aAAa,CAACY,GAAG,CAACC,YAAY,iBAC7Bf,OAAA;QAEEO,SAAS,EAAE,qBACTJ,mBAAmB,KAAKY,YAAY,CAACC,EAAE,GAAG,QAAQ,GAAG,EAAE,EACtD;QACHH,OAAO,EAAEA,CAAA,KAAMT,oBAAoB,CAACW,YAAY,CAACC,EAAE,CAAE;QAAAR,QAAA,gBAErDR,OAAA;UAAKO,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAChCO,YAAY,CAACE;QAAK;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACNZ,OAAA;UAAKO,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAC/B,IAAIU,IAAI,CAACH,YAAY,CAACI,UAAU,CAAC,CAACC,kBAAkB,CAAC;QAAC;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA,GAXDG,YAAY,CAACC,EAAE;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAYjB,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENZ,OAAA;MAAKO,SAAS,EAAC,gBAAgB;MAAAC,QAAA,eAC7BR,OAAA;QAAKO,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBR,OAAA;UAAKO,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrCZ,OAAA;UAAKO,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAEF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEe;QAAQ;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACU,EAAA,GA/CIrB,OAAO;AAiDb,eAAeA,OAAO;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}