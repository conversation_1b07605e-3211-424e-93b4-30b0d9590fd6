import React from 'react';
import './Sidebar.css';

const Sidebar = ({ 
  conversations, 
  currentConversation, 
  onSelectConversation, 
  onNewConversation,
  user 
}) => {
  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <div className="logo-section">
          <div className="logo-icon">🤖</div>
          <h2>Sozhaa Tech AI</h2>
        </div>
        <button className="new-chat-btn" onClick={onNewConversation}>
          <span className="btn-icon">✨</span>
          New Chat
        </button>
      </div>
      
      <div className="conversations-list">
        {conversations.map(conversation => (
          <div
            key={conversation.id}
            className={`conversation-item ${
              currentConversation === conversation.id ? 'active' : ''
            }`}
            onClick={() => onSelectConversation(conversation.id)}
          >
            <div className="conversation-title">
              {conversation.title}
            </div>
            <div className="conversation-date">
              {new Date(conversation.updated_at).toLocaleDateString()}
            </div>
          </div>
        ))}
      </div>
      
      <div className="sidebar-footer">
        <div className="user-info">
          <div className="user-avatar">👤</div>
          <div className="user-name">{user?.username}</div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;