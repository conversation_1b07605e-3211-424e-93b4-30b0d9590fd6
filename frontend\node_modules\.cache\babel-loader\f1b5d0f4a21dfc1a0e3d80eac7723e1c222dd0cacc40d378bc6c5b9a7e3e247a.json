{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Deep Learning\\\\LLM\\\\frontend\\\\src\\\\components\\\\Message.js\";\nimport React from 'react';\nimport './Message.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Message = ({\n  message,\n  isUser\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `message ${isUser ? 'user-message' : 'assistant-message'}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message-avatar\",\n      children: isUser ? '👤' : '🤖'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"message-content\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-text\",\n        children: message.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this), message.metadata && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-metadata\",\n        children: [message.metadata.used_agent && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"meta-tag\",\n          children: \"Agent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 45\n        }, this), message.metadata.used_rag && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"meta-tag\",\n          children: \"RAG\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 43\n        }, this), message.metadata.tools_used && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"meta-tag\",\n          children: [\"Tools: \", message.metadata.tools_used.join(', ')]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"message-timestamp\",\n        children: new Date(message.timestamp).toLocaleTimeString()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = Message;\nexport default Message;\nvar _c;\n$RefreshReg$(_c, \"Message\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Message", "message", "isUser", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "metadata", "used_agent", "used_rag", "tools_used", "join", "Date", "timestamp", "toLocaleTimeString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Deep Learning/LLM/frontend/src/components/Message.js"], "sourcesContent": ["import React from 'react';\r\nimport './Message.css';\r\n\r\nconst Message = ({ message, isUser }) => {\r\n  return (\r\n    <div className={`message ${isUser ? 'user-message' : 'assistant-message'}`}>\r\n      <div className=\"message-avatar\">\r\n        {isUser ? '👤' : '🤖'}\r\n      </div>\r\n      <div className=\"message-content\">\r\n        <div className=\"message-text\">\r\n          {message.content}\r\n        </div>\r\n        {message.metadata && (\r\n          <div className=\"message-metadata\">\r\n            {message.metadata.used_agent && <span className=\"meta-tag\">Agent</span>}\r\n            {message.metadata.used_rag && <span className=\"meta-tag\">RAG</span>}\r\n            {message.metadata.tools_used && (\r\n              <span className=\"meta-tag\">Tools: {message.metadata.tools_used.join(', ')}</span>\r\n            )}\r\n          </div>\r\n        )}\r\n        <div className=\"message-timestamp\">\r\n          {new Date(message.timestamp).toLocaleTimeString()}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Message;"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvB,MAAMC,OAAO,GAAGA,CAAC;EAAEC,OAAO;EAAEC;AAAO,CAAC,KAAK;EACvC,oBACEH,OAAA;IAAKI,SAAS,EAAE,WAAWD,MAAM,GAAG,cAAc,GAAG,mBAAmB,EAAG;IAAAE,QAAA,gBACzEL,OAAA;MAAKI,SAAS,EAAC,gBAAgB;MAAAC,QAAA,EAC5BF,MAAM,GAAG,IAAI,GAAG;IAAI;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eACNT,OAAA;MAAKI,SAAS,EAAC,iBAAiB;MAAAC,QAAA,gBAC9BL,OAAA;QAAKI,SAAS,EAAC,cAAc;QAAAC,QAAA,EAC1BH,OAAO,CAACQ;MAAO;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,EACLP,OAAO,CAACS,QAAQ,iBACfX,OAAA;QAAKI,SAAS,EAAC,kBAAkB;QAAAC,QAAA,GAC9BH,OAAO,CAACS,QAAQ,CAACC,UAAU,iBAAIZ,OAAA;UAAMI,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACtEP,OAAO,CAACS,QAAQ,CAACE,QAAQ,iBAAIb,OAAA;UAAMI,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EAClEP,OAAO,CAACS,QAAQ,CAACG,UAAU,iBAC1Bd,OAAA;UAAMI,SAAS,EAAC,UAAU;UAAAC,QAAA,GAAC,SAAO,EAACH,OAAO,CAACS,QAAQ,CAACG,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;QAAA;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CACjF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,eACDT,OAAA;QAAKI,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAC/B,IAAIW,IAAI,CAACd,OAAO,CAACe,SAAS,CAAC,CAACC,kBAAkB,CAAC;MAAC;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9C,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACU,EAAA,GAzBIlB,OAAO;AA2Bb,eAAeA,OAAO;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}